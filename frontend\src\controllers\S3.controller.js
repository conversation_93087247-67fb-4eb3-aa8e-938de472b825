import environment from "../../environment.js";
import axiosInstance from "../axios.js";
import idb from "../indexedDB.js";

class S3Controller {
    constructor() {
        this.cacheStore = "s3_url_cache";
    }

    async getCachedUrl(cacheKey) {
        try {
            const cachedItem = await idb.getItem(this.cacheStore, cacheKey);

            if (!cachedItem) {
                return null;
            }

            const now = Date.now();

            if (cachedItem.expiresAt && now >= cachedItem.expiresAt) {
                await idb.deleteItem(this.cacheStore, cacheKey);
                return null;
            }

            return cachedItem.url;
        } catch (error) {
            console.error("[S3Controller] Error getting cached URL:", error);
            return null;
        }
    }

    getExpiryFromUrl(signedUrl) {
        try {
            const url = new URL(signedUrl);
            const expires = url.searchParams.get("Expires") || url.searchParams.get("X-Amz-Expires");
            if (expires) {
                return parseInt(expires) * 1000;
            }
            return null;
        } catch (error) {
            console.error("[S3Controller] Error extracting expiry from URL:", error);
            return null;
        }
    }

    async fetchS3SignedUrl(key, bucketName, region) {
        try {
            const cacheKey = `signed_url_${key}`;
            const cachedUrl = await this.getCachedUrl(cacheKey);
            if (cachedUrl) {
                return cachedUrl;
            }

            const response = await axiosInstance.get("/s3/signedUrl", {
                params: { key: key, bucket_name: bucketName, region: region },
            });

            const signedUrl = response.data.signedUrl;

            if (signedUrl) {
                const expiresAt = this.getExpiryFromUrl(signedUrl);

                if (expiresAt) {
                    await idb.addItems(this.cacheStore, [
                        {
                            _id: cacheKey,
                            url: signedUrl,
                            expiresAt: expiresAt,
                            timestamp: Date.now(),
                        },
                    ]);
                } else {
                    console.warn(`[S3Controller] Could not extract expiry from URL, not caching: ${key}`);
                }
            } else {
                console.warn(`[S3Controller] No signed URL returned for ${key}, not caching`);
            }

            return signedUrl;
        } catch (error) {
            console.error("Error fetching signed URL:", error);
            throw error;
        }
    }

    async fetchCloudfrontSignedUrl(key, bucketName) {
        try {
            const cacheKey = `signed_url_${key}`;

            const cachedUrl = await this.getCachedUrl(cacheKey);
            if (cachedUrl) {
                return cachedUrl;
            }

            const response = await axiosInstance.get("/s3/cloudfront/signedUrl", {
                params: { key: key, bucketName: bucketName },
            });

            const signedUrl = response.data.signedUrl;

            if (signedUrl) {
                const expiresAt = this.getExpiryFromUrl(signedUrl);

                if (expiresAt) {
                    await idb.addItems(this.cacheStore, [
                        {
                            _id: cacheKey,
                            url: signedUrl,
                            expiresAt: expiresAt,
                            timestamp: Date.now(),
                        },
                    ]);
                } else {
                    console.warn(`[S3Controller] Could not extract expiry from URL, not caching: ${key}`);
                }
            }

            return signedUrl;
        } catch (error) {
            console.error("Error fetching signed URL:", error);
            throw error;
        }
    }

    fetchUrl(artifact, linkType = undefined) {
        return `${environment.VITE_API_URL}/api/artifacts${linkType ? `/${linkType}` : ""}/link/${artifact._id}${linkType === "video" ? `.mp4` : ""}`;
    }

    fetchPreviewUrl(artifact) {
        return this.fetchUrl(artifact, "thumbnail_image");
    }
}
const s3Controller = new S3Controller();

export default s3Controller;
